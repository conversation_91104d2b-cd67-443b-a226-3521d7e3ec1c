/**
 * 商品识别保存功能修复验证脚本
 * 
 * 验证handleSaveMarkings函数中的validMarkings过滤逻辑修复
 */

// 模拟TimelineSegment数据结构
const createMockSegment = (startTime, endTime, status = 'pending', itemId = null, itemName = null, isManuallySelected = false) => ({
  startTime,
  endTime,
  status,
  itemId,
  itemName,
  isManuallySelected
});

// 模拟状态检测函数
const detectUnsavedChanges = (currentSegments, lastSavedState) => {
  if (!lastSavedState) {
    return currentSegments.some(segment => 
      (segment.status === 'completed' || segment.status === 'saved') && segment.itemId
    );
  }
  
  return currentSegments.some((current, index) => {
    const saved = lastSavedState[index];
    if (!saved) return current.itemId;
    
    return current.itemId !== saved.itemId || 
           current.itemName !== saved.itemName ||
           current.isManuallySelected !== saved.isManuallySelected;
  });
};

// 模拟保存函数的验证逻辑
const validateSaveOperation = (timelineSegments, hasUnsavedChanges) => {
  // 新的过滤逻辑：包含completed和saved状态的片段
  const validMarkings = timelineSegments.filter(segment =>
    (segment.status === 'completed' || segment.status === 'saved') && segment.itemId
  );

  if (validMarkings.length === 0) {
    return { canSave: false, reason: '没有可保存的识别结果，请先完成商品识别' };
  }

  if (!hasUnsavedChanges) {
    return { canSave: false, reason: '当前没有未保存的修改' };
  }

  return { canSave: true, validMarkings };
};

console.log('🧪 开始测试保存功能修复...\n');

// 测试场景1: 新识别完成
console.log('📋 测试场景1: 新识别完成');
const newRecognitionSegments = [
  createMockSegment(0, 10000, 'completed', 'item1', '商品1'),
  createMockSegment(10000, 20000, 'pending')
];
const hasChanges1 = detectUnsavedChanges(newRecognitionSegments, null);
const validation1 = validateSaveOperation(newRecognitionSegments, hasChanges1);
console.log(`  有未保存修改: ${hasChanges1 ? '是' : '否'}`);
console.log(`  可以保存: ${validation1.canSave ? '✅ 是' : '❌ 否'}`);
if (!validation1.canSave) console.log(`  原因: ${validation1.reason}`);
if (validation1.validMarkings) console.log(`  有效片段数: ${validation1.validMarkings.length}`);
console.log('');

// 测试场景2: 加载历史数据，无修改
console.log('📋 测试场景2: 加载历史数据，无修改');
const historicalSegments = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000, 'pending')
];
const lastSaved = [...historicalSegments];
const hasChanges2 = detectUnsavedChanges(historicalSegments, lastSaved);
const validation2 = validateSaveOperation(historicalSegments, hasChanges2);
console.log(`  有未保存修改: ${hasChanges2 ? '是' : '否'}`);
console.log(`  可以保存: ${validation2.canSave ? '✅ 是' : '❌ 否'}`);
if (!validation2.canSave) console.log(`  原因: ${validation2.reason}`);
if (validation2.validMarkings) console.log(`  有效片段数: ${validation2.validMarkings.length}`);
console.log('');

// 测试场景3: 加载历史数据后手动编辑 (核心修复场景)
console.log('📋 测试场景3: 加载历史数据后手动编辑 (核心修复场景)');
const editedSegments = [
  createMockSegment(0, 10000, 'saved', 'item2', '商品2', true), // 手动修改了商品
  createMockSegment(10000, 20000, 'pending')
];
const hasChanges3 = detectUnsavedChanges(editedSegments, lastSaved);
const validation3 = validateSaveOperation(editedSegments, hasChanges3);
console.log(`  有未保存修改: ${hasChanges3 ? '是' : '否'}`);
console.log(`  可以保存: ${validation3.canSave ? '✅ 是' : '❌ 否'}`);
if (!validation3.canSave) console.log(`  原因: ${validation3.reason}`);
if (validation3.validMarkings) console.log(`  有效片段数: ${validation3.validMarkings.length}`);
console.log('');

// 测试场景4: 混合场景（历史数据 + 新识别 + 手动编辑）
console.log('📋 测试场景4: 混合场景（历史数据 + 新识别 + 手动编辑）');
const mixedSegments = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),           // 历史数据
  createMockSegment(10000, 20000, 'completed', 'item2', '商品2'),   // 新识别
  createMockSegment(20000, 30000, 'saved', 'item3', '商品3修改', true), // 手动编辑
  createMockSegment(30000, 40000, 'pending')                       // 未处理
];
const mixedLastSaved = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000, 'pending'),
  createMockSegment(20000, 30000, 'saved', 'item3', '商品3'),
  createMockSegment(30000, 40000, 'pending')
];
const hasChanges4 = detectUnsavedChanges(mixedSegments, mixedLastSaved);
const validation4 = validateSaveOperation(mixedSegments, hasChanges4);
console.log(`  有未保存修改: ${hasChanges4 ? '是' : '否'}`);
console.log(`  可以保存: ${validation4.canSave ? '✅ 是' : '❌ 否'}`);
if (!validation4.canSave) console.log(`  原因: ${validation4.reason}`);
if (validation4.validMarkings) console.log(`  有效片段数: ${validation4.validMarkings.length}`);
console.log('');

// 测试场景5: 空数据
console.log('📋 测试场景5: 空数据');
const emptySegments = [
  createMockSegment(0, 10000, 'pending'),
  createMockSegment(10000, 20000, 'pending')
];
const hasChanges5 = detectUnsavedChanges(emptySegments, null);
const validation5 = validateSaveOperation(emptySegments, hasChanges5);
console.log(`  有未保存修改: ${hasChanges5 ? '是' : '否'}`);
console.log(`  可以保存: ${validation5.canSave ? '✅ 是' : '❌ 否'}`);
if (!validation5.canSave) console.log(`  原因: ${validation5.reason}`);
console.log('');

// 测试场景6: 已保存状态（刚保存完）
console.log('📋 测试场景6: 已保存状态（刚保存完）');
const justSavedSegments = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000, 'saved', 'item2', '商品2')
];
const justSavedState = [...justSavedSegments];
const hasChanges6 = detectUnsavedChanges(justSavedSegments, justSavedState);
const validation6 = validateSaveOperation(justSavedSegments, hasChanges6);
console.log(`  有未保存修改: ${hasChanges6 ? '是' : '否'}`);
console.log(`  可以保存: ${validation6.canSave ? '✅ 是' : '❌ 否'}`);
if (!validation6.canSave) console.log(`  原因: ${validation6.reason}`);
if (validation6.validMarkings) console.log(`  有效片段数: ${validation6.validMarkings.length}`);
console.log('');

console.log('✅ 保存功能测试完成！');
console.log('');
console.log('📝 修复总结:');
console.log('1. ✅ 修复了validMarkings过滤条件，现在包含saved状态的片段');
console.log('2. ✅ 新识别完成后可以正常保存');
console.log('3. ✅ 加载历史数据后手动编辑可以正常保存');
console.log('4. ✅ 混合场景下保存功能正常');
console.log('5. ✅ 无修改时正确提示"当前没有未保存的修改"');
console.log('6. ✅ 空数据时正确提示"没有可保存的识别结果"');
