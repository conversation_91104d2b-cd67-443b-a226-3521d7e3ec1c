/**
 * 商品识别保存按钮状态修复验证脚本
 * 
 * 这个脚本可以在浏览器控制台中运行，用于验证修复后的按钮状态逻辑
 */

// 模拟TimelineSegment数据结构
const createMockSegment = (startTime, endTime, status = 'pending', itemId = null, itemName = null) => ({
  startTime,
  endTime,
  status,
  itemId,
  itemName,
  isManuallySelected: false
});

// 模拟状态检测函数
const detectUnsavedChanges = (currentSegments, lastSavedState) => {
  if (!lastSavedState) {
    // 没有保存过的状态，任何有效片段都算未保存
    return currentSegments.some(segment => 
      (segment.status === 'completed' || segment.status === 'saved') && segment.itemId
    );
  }
  
  // 比较当前状态与上次保存的状态
  return currentSegments.some((current, index) => {
    const saved = lastSavedState[index];
    if (!saved) return current.itemId; // 新增片段
    
    // 检查关键字段是否变化
    return current.itemId !== saved.itemId || 
           current.itemName !== saved.itemName ||
           current.isManuallySelected !== saved.isManuallySelected;
  });
};

// 测试用例
console.log('🧪 开始测试商品识别保存按钮状态修复...\n');

// 测试1: 初始状态（无数据）
console.log('📋 测试1: 初始状态（无数据）');
const emptySegments = [
  createMockSegment(0, 10000),
  createMockSegment(10000, 20000)
];
const hasChanges1 = detectUnsavedChanges(emptySegments, null);
console.log(`结果: ${hasChanges1 ? '❌ 有未保存修改' : '✅ 无未保存修改'} (预期: 无)`);
console.log('');

// 测试2: 新识别完成
console.log('📋 测试2: 新识别完成');
const newRecognitionSegments = [
  createMockSegment(0, 10000, 'completed', 'item1', '商品1'),
  createMockSegment(10000, 20000)
];
const hasChanges2 = detectUnsavedChanges(newRecognitionSegments, null);
console.log(`结果: ${hasChanges2 ? '✅ 有未保存修改' : '❌ 无未保存修改'} (预期: 有)`);
console.log('');

// 测试3: 加载历史数据后无修改
console.log('📋 测试3: 加载历史数据后无修改');
const historicalSegments = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000)
];
const lastSaved = [...historicalSegments]; // 模拟刚加载的状态
const hasChanges3 = detectUnsavedChanges(historicalSegments, lastSaved);
console.log(`结果: ${hasChanges3 ? '❌ 有未保存修改' : '✅ 无未保存修改'} (预期: 无)`);
console.log('');

// 测试4: 加载历史数据后手动编辑
console.log('📋 测试4: 加载历史数据后手动编辑');
const editedSegments = [
  { ...historicalSegments[0], itemId: 'item2', itemName: '商品2', isManuallySelected: true },
  historicalSegments[1]
];
const hasChanges4 = detectUnsavedChanges(editedSegments, lastSaved);
console.log(`结果: ${hasChanges4 ? '✅ 有未保存修改' : '❌ 无未保存修改'} (预期: 有)`);
console.log('');

// 测试5: 混合场景（历史数据 + 新识别）
console.log('📋 测试5: 混合场景（历史数据 + 新识别）');
const mixedSegments = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000, 'completed', 'item2', '商品2'), // 新识别
  createMockSegment(20000, 30000)
];
const mixedLastSaved = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000),
  createMockSegment(20000, 30000)
];
const hasChanges5 = detectUnsavedChanges(mixedSegments, mixedLastSaved);
console.log(`结果: ${hasChanges5 ? '✅ 有未保存修改' : '❌ 无未保存修改'} (预期: 有)`);
console.log('');

// 测试6: 保存后状态
console.log('📋 测试6: 保存后状态');
const savedSegments = [
  createMockSegment(0, 10000, 'saved', 'item1', '商品1'),
  createMockSegment(10000, 20000, 'saved', 'item2', '商品2')
];
const newLastSaved = [...savedSegments]; // 模拟保存后的状态
const hasChanges6 = detectUnsavedChanges(savedSegments, newLastSaved);
console.log(`结果: ${hasChanges6 ? '❌ 有未保存修改' : '✅ 无未保存修改'} (预期: 无)`);
console.log('');

// 按钮状态模拟
const simulateButtonState = (segments, lastSavedState, isSaving = false) => {
  const hasUnsavedChanges = detectUnsavedChanges(segments, lastSavedState);
  const disabled = isSaving || !hasUnsavedChanges;
  const buttonText = hasUnsavedChanges ? "保存修改" : "已保存";
  const buttonType = hasUnsavedChanges ? "primary" : "default";
  
  return { disabled, buttonText, buttonType, hasUnsavedChanges };
};

console.log('🎯 按钮状态模拟测试:');
console.log('');

// 场景1: 新识别完成
const buttonState1 = simulateButtonState(newRecognitionSegments, null);
console.log('场景1 - 新识别完成:');
console.log(`  按钮文本: "${buttonState1.buttonText}"`);
console.log(`  按钮类型: ${buttonState1.buttonType}`);
console.log(`  是否禁用: ${buttonState1.disabled ? '是' : '否'}`);
console.log('');

// 场景2: 加载历史数据
const buttonState2 = simulateButtonState(historicalSegments, lastSaved);
console.log('场景2 - 加载历史数据:');
console.log(`  按钮文本: "${buttonState2.buttonText}"`);
console.log(`  按钮类型: ${buttonState2.buttonType}`);
console.log(`  是否禁用: ${buttonState2.disabled ? '是' : '否'}`);
console.log('');

// 场景3: 手动编辑后
const buttonState3 = simulateButtonState(editedSegments, lastSaved);
console.log('场景3 - 手动编辑后:');
console.log(`  按钮文本: "${buttonState3.buttonText}"`);
console.log(`  按钮类型: ${buttonState3.buttonType}`);
console.log(`  是否禁用: ${buttonState3.disabled ? '是' : '否'}`);
console.log('');

console.log('✅ 所有测试完成！');
console.log('');
console.log('📝 测试总结:');
console.log('- 新识别完成后按钮正确启用');
console.log('- 加载历史数据后按钮正确禁用');
console.log('- 手动编辑后按钮立即启用');
console.log('- 保存后按钮正确禁用');
console.log('- 按钮文本和样式正确反映状态');
