# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FastClip is a comprehensive video content management and automated editing platform for e-commerce live streaming. It combines AI-powered product recognition, automated video clipping, and content management capabilities with integration to Douyin (TikTok) and other Chinese e-commerce platforms.

## Development Commands

### Backend (Spring Boot Maven Multi-Module)
- **Build**: `cd fastclip-backend-service && mvn clean compile`
- **Package**: `cd fastclip-backend-service && mvn clean package`
- **Run**: `cd fastclip-backend-service/fastclip-starter && mvn spring-boot:run`
- **Test**: `cd fastclip-backend-service && mvn test`

The backend runs on port 8078 with context path `/api/fastclip`.

### Frontend (React + Umi Max)
- **Development**: `cd fastclip-front-service && npm run dev` (or `npm start`)
- **Build**: `cd fastclip-front-service && npm run build`
- **Format**: `cd fastclip-front-service && npm run format`
- **Setup**: `cd fastclip-front-service && npm run setup`

## Architecture Overview

### Backend Module Structure
The Spring Boot backend is organized into 7 Maven modules:

1. **fastclip-starter** - Application entry point, configuration, and Spring Boot runner
2. **fastclip-biz** - REST controllers and WebSocket endpoints for API layer
3. **fastclip-service** - Core business services (video processing, AI integration)
4. **fastclip-dao** - Data access layer with MyBatis, Redis, and database models
5. **fastclip-common** - Shared utilities, constants, models, and DTOs
6. **fastclip-opencv** - Video processing using OpenCV, FFmpeg, and computer vision
7. **fastclip-llm** - Language model integration (Alibaba Tongyi/Qwen)

### Key Integration Points

#### API Communication Pattern
Frontend-backend communication follows a consistent pattern:
- Frontend: `src/services/workbench/WorkbenchController.ts` 
- Backend: `fastclip-biz/src/main/java/com/fastclip/biz/controller/WorkbenchController.java`
- All requests use POST method with JSON body via `/api/fastclip/` prefix

#### Video Processing Pipeline
- **Input**: Live stream URLs, uploaded videos
- **Processing**: `fastclip-opencv` module handles FFmpeg operations, OpenCV computer vision
- **Output**: HLS segments, clipped videos, product recognition results
- **Storage**: File system paths configured in `application.yml` under `ffmpeg.*` and `video.*`

#### Database Architecture
- **Primary DB**: MySQL with comprehensive schema for videos, products, users, projects
- **Cache**: Redis for session management, real-time data, and performance optimization
- **Files**: Database schema definitions in `fastclip-dao/src/main/resources/sql/`

#### Real-time Features
- **WebSockets**: Product recognition progress updates via `VideoPlayWebSocket.java`
- **Live Streaming**: RTMP server integration for live video capture
- **HLS Streaming**: Video-on-demand playback with segmented streaming

### Configuration Management

#### Environment-Specific Settings
Key configuration is in `fastclip-starter/src/main/resources/application.yml`:

- **Database**: MySQL connection with environment variable overrides
- **External APIs**: Douyin OpenAPI, Chanxuan platform integration
- **File Paths**: Video storage, processing directories (configurable via env vars)
- **AI Services**: Tongyi LLM API keys and prompts
- **Video Processing**: FFmpeg settings, HLS configuration

#### Authentication & Security
- **JWT-based auth** with configurable ignore paths in `auth.ignoreList`
- **CORS support** for frontend-backend communication
- **File upload limits**: 4GB max file size

## Development Workflow

### Adding New Features
1. **Backend**: Add controller method in `fastclip-biz`, implement service in appropriate module
2. **Frontend**: Add API call in `src/services/`, create/update components in `src/pages/`
3. **Database**: Add schema changes in `fastclip-dao/src/main/resources/sql/`

### Video Processing Features
- **Core logic**: Implement in `fastclip-opencv` module using JavaCV/FFmpeg wrappers
- **Special effects**: Extend `SpecialEffectAbstract` classes for new effects
- **Timeline processing**: Work with `VideoMaterialSlice` and `VideoClip` entities

### AI/ML Integration
- **Product recognition**: Extend computer vision capabilities in `fastclip-opencv`
- **LLM features**: Add new AI services in `fastclip-llm` module
- **Real-time updates**: Use WebSocket patterns for progress notifications

## Important Notes

### External Dependencies
- **FFmpeg**: Required for video processing (GPU/CPU configurable via `PROCESSOR` env var)
- **MySQL**: Database server (port configurable via connection string)
- **Redis**: Cache server for session management
- **Douyin APIs**: Requires valid API keys and authentication tokens

### File System Requirements
- **Large storage**: Video files can be up to 4GB
- **Directory structure**: Configured paths in `application.yml` must exist and be writable
- **HLS segments**: Real-time generation requires sufficient disk I/O performance

### Platform-Specific Features
- **Chinese e-commerce**: Heavy integration with Douyin, Chanxuan platforms
- **Live streaming**: RTMP server setup required for live capture features
- **Product recognition**: Computer vision models may require additional setup