.productMarkingModal {
  .modalContent {
    max-height: 85vh;
    overflow-y: auto;
  }

  .ant-card-head-title {
    font-size: 14px;
    font-weight: 500;
  }

  .ant-card-body {
    padding: 12px;
  }

  .ant-row {
    margin-bottom: 0;
  }

  .ant-col {
    margin-bottom: 8px;
  }
}


.segment-progress-bar {
  .wrapper {
    display: flex;
    height: 42px;
    background: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    position: relative;
  }

  .segment {
    position: relative;
    border-right: 1px solid #fff;
    background: #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 20px;
    flex-direction: column;
    padding: 2px;

    &:last-child {
      border-right: none;
    }

    &:hover {
      background: #bfbfbf;
      transform: scaleY(1.1);
      z-index: 2;
    }

    &.active {
      background: #1890ff;
      box-shadow: 0 0 8px rgba(24, 144, 255, 0.6);
      z-index: 3;
    }

    &.pending {
      background: #f0f0f0;
      color: #666;
    }

    &.processing {
      background: #1890ff;
      color: white;
      opacity: 0.6;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shimmer 1.5s infinite;
      }
    }

    &.completed {
      color: white;
      font-weight: 500;
    }

    &.error {
      background: #ff4d4f;
      color: white;
    }



    .segmentTime {
      font-size: 8px;
      line-height: 1;
      margin-bottom: 1px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .segmentLabel {
      font-size: 9px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .confidenceScore {
      font-size: 7px;
      opacity: 0.8;
      margin-top: 1px;
    }

    .time-tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s ease;
      margin-bottom: 4px;
      z-index: 10;
    }

    &:hover .time-tooltip {
      opacity: 1;
    }
  }

  .legend {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #666;

      .color-dot {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// Product legend styles for recognition results
.productLegend {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;

  .legendTitle {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    display: block;
  }

  .legendItems {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
  }

  .legendItem {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    font-size: 12px;

    .colorDot {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      flex-shrink: 0;
    }

    .itemId {
      font-weight: 500;
      color: #1890ff;
      margin-right: 4px;
    }

    .itemName {
      color: #666;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}


.timelineContainer {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  // min-height: 200px;

  .timelineInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
    border-top: 1px solid #e8e8e8;
    padding-top: 12px;
    margin-top: 16px;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// Custom styles for product selection context menu
:global(.ant-dropdown-menu) {
  .ant-dropdown-menu-item {
    padding: 2px 8px;

    &:hover {
      .product-menu-item {
        background-color: #f8f9fa;
        border-radius: 6px;
        transform: translateX(2px);
      }
    }

    &:active {
      .product-menu-item {
        background-color: #e9ecef;
      }
    }
  }

  .ant-dropdown-menu-item-group-title {
    padding: 8px 12px 4px;
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }
}

.product-menu-item {
  transition: background-color 0.2s ease;

  .color-indicator {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .product-info {
    .product-id {
      line-height: 1.2;
    }

    .product-name {
      line-height: 1.2;
      margin-top: 1px;
    }
  }

  .confidence-badge {
    background: #f0f0f0;
    padding: 1px 6px;
    border-radius: 10px;
    font-family: 'Monaco', 'Menlo', monospace;
  }
}

// Video Player Styles
.video-player-container {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;

  .video-js {
    width: 100% !important;
    height: 100% !important;
    max-height: 480px !important;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: #fafafa !important;

    // Video loading and poster area
    .vjs-poster {
      background-color: #fafafa !important;
    }

    .vjs-loading-spinner {
      border-color: #1890ff transparent transparent transparent;
    }

    .vjs-big-play-button {
      background-color: rgba(24, 144, 255, 0.8);
      border: none;
      border-radius: 50%;
      width: 80px;
      height: 80px;
      line-height: 80px;
      font-size: 24px;

      &:hover {
        background-color: rgba(24, 144, 255, 1);
      }
    }

    .vjs-control-bar {
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);

      .vjs-play-control,
      .vjs-volume-control,
      .vjs-time-control,
      .vjs-fullscreen-control {
        color: white;
      }

      .vjs-progress-control {
        .vjs-progress-holder {
          .vjs-play-progress {
            background-color: #1890ff;
          }

          .vjs-load-progress {
            background-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    // Video markers styling
    .vjs-marker {
      background-color: #fa8c16 !important;
      height: 8px !important;
      border-radius: 2px !important;
      cursor: pointer;

      &:hover {
        background-color: #d46b08 !important;
        transform: scaleY(1.2);
      }
    }

    // Video tech area (where video content displays)
    .vjs-tech {
      background-color: #fafafa !important;
    }

    // Video display area
    video {
      background-color: #fafafa !important;
    }
  }

  // Loading state styling
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fafafa;
    z-index: -1;
  }
}

// Clip List Styles
.clip-list-container {
  .ant-card-body {
    padding: 12px;
    max-height: 350px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .clip-button {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .clip-card {
    transition: all 0.2s ease;

    &:hover {
      transform: translateX(2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

@media (max-width: 768px) {
  .timelineContainer {
    .timelineGrid {
      grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
      gap: 6px;
    }

    .timelineSegment {
      height: 50px;

      .segmentTime {
        font-size: 9px;
      }

      .segmentLabel {
        font-size: 10px;
      }
    }
  }

  // Mobile responsive for video player
  .video-player-container {
    .video-js {
      .vjs-big-play-button {
        width: 60px;
        height: 60px;
        line-height: 60px;
        font-size: 18px;
      }
    }
  }
}
