import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal, Card, Row, Col, Tag, Spin, Button, Progress, message } from 'antd';
import { PlayCircleOutlined, ClockCircleOutlined, StopOutlined, SaveOutlined } from '@ant-design/icons';
import VideoTimeline from './VideoTimeline';
import VideoPlayer from './VideoPlayer';
import styles from './ProductMarkingModal.less';
import { request } from '@/utils/request';
// import videojs from 'video.js';

interface ProductMarkingModalProps {
  visible: boolean;
  onCancel: () => void;
  videoRecord: any;
}

interface TimelineSegment {
  startTime: number;
  endTime: number;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'saved';
  productType?: number;
  confidence?: number;
  itemId?: string;
  itemName?: string;
  // Manual selection support
  isManuallySelected?: boolean;
  originalItemId?: string;
  originalItemName?: string;
  originalConfidence?: number;
  // Store all recognition results for manual selection
  allRecognitionResults?: Array<{
    itemId: string;
    itemName: string;
    confidence: number;
  }>;
  // Save status
  isSaved?: boolean;
  savedId?: number;
}

interface WebSocketMessage {
  type: 'connection' | 'progress_update' | 'recognition_result' | 'recognition_complete' | 'error' | 'pong';
  taskId?: string;
  sessionId?: string;
  status?: string;
  currentSegment?: number;
  totalSegments?: number;
  percentage?: number;
  timestamp?: number;
  segmentIndex?: number;
  itemType?: number;
  confidence?: number;
  itemId?: string;
  itemName?: string;
  message?: string;
}

enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

const ProductMarkingModal: React.FC<ProductMarkingModalProps> = ({
  visible,
  onCancel,
  videoRecord
}) => {
  const [timelineSegments, setTimelineSegments] = useState<TimelineSegment[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED);
  const [currentTaskId, setCurrentTaskId] = useState<string>('');
  const [totalSegments, setTotalSegments] = useState(0);
  const [currentSegment, setCurrentSegment] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [timelineInitialized, setTimelineInitialized] = useState(false);

  // Enhanced state management for save button
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSavedState, setLastSavedState] = useState<TimelineSegment[] | null>(null);

  // Video player related states
  const [videoPlayer, setVideoPlayer] = useState<any>(null);
  const [hlsVideoUrl, setHlsVideoUrl] = useState<string>('');
  const [currentVideoTime, setCurrentVideoTime] = useState<number>(0);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket connection management
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected, skipping...');
      return;
    }

    console.log('=== Starting WebSocket Connection ===');
    setConnectionStatus(ConnectionStatus.CONNECTING);

    // Use proxy path for WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/api/fastclip/ws/product-recognition`;

    console.log('WebSocket URL:', wsUrl);
    console.log('Current location:', window.location);
    console.log('Protocol:', protocol);
    console.log('Host:', window.location.host);
    console.log('Expected final URL after proxy:', `ws://localhost:8078/ws/product-recognition`);

    try {
      console.log('Creating WebSocket connection...');
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = (event) => {
        console.log('=== WebSocket Connected Successfully ===');
        console.log('Event:', event);
        console.log('ReadyState:', wsRef.current?.readyState);
        console.log('URL:', wsRef.current?.url);
        console.log('Protocol:', wsRef.current?.protocol);
        console.log('=======================================');

        setConnectionStatus(ConnectionStatus.CONNECTED);
        startHeartbeat();
      };

      wsRef.current.onmessage = (event) => {
        console.log('WebSocket message received:', event.data);
        try {
          const wsMessage: WebSocketMessage = JSON.parse(event.data);
          console.log('Parsed message:', wsMessage);
          handleWebSocketMessage(wsMessage);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error, 'Raw data:', event.data);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('=== WebSocket Connection Closed ===');
        console.log('Code:', event.code);
        console.log('Reason:', event.reason);
        console.log('WasClean:', event.wasClean);
        console.log('==================================');

        setConnectionStatus(ConnectionStatus.DISCONNECTED);
        stopHeartbeat();

        // Auto-reconnect after 3 seconds if not a clean close
        if (!event.wasClean) {
          console.log('Scheduling reconnection in 3 seconds...');
          reconnectTimeoutRef.current = setTimeout(connectWebSocket, 3000);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('=== WebSocket Error ===');
        console.error('Error event:', error);
        console.error('ReadyState:', wsRef.current?.readyState);
        console.error('URL:', wsRef.current?.url);
        console.error('=====================');

        setConnectionStatus(ConnectionStatus.ERROR);
      };

      console.log('WebSocket event handlers attached');

    } catch (error) {
      console.error('=== Failed to Create WebSocket ===');
      console.error('Error:', error);
      console.error('URL:', wsUrl);
      console.error('================================');
      setConnectionStatus(ConnectionStatus.ERROR);
    }
  }, []);

  const handleWebSocketMessage = useCallback((wsMessage: WebSocketMessage) => {
    console.log('Received WebSocket message:', wsMessage);

    switch (wsMessage.type) {
      case 'connection':
        console.log('WebSocket connection confirmed:', wsMessage.sessionId);
        break;

      case 'progress_update':
        if (wsMessage.currentSegment && wsMessage.totalSegments && wsMessage.percentage !== undefined) {
          setCurrentSegment(wsMessage.currentSegment);
          setTotalSegments(wsMessage.totalSegments);
          setProcessingProgress(wsMessage.percentage);
        }
        break;

      case 'recognition_result':
        if (wsMessage.segmentIndex !== undefined) {
          updateSegmentResult(
            wsMessage.segmentIndex,
            wsMessage.itemType || 0,
            wsMessage.confidence || 0,
            wsMessage.itemId,
            wsMessage.itemName
          );
        }
        break;

      case 'recognition_complete':
        setIsProcessing(false);
        message.success('商品识别完成！');
        break;

      case 'error':
        setIsProcessing(false);
        message.error(`识别失败: ${wsMessage.message}`);
        break;

      case 'pong':
        // Heartbeat response
        break;

      default:
        console.warn('Unknown message type:', wsMessage.type);
    }
  }, []);

  // Detect if there are unsaved changes by comparing current state with last saved state
  const detectUnsavedChanges = useCallback((currentSegments: TimelineSegment[]) => {
    if (!lastSavedState) {
      // No saved state means any completed/saved segments with itemId are unsaved changes
      return currentSegments.some(segment =>
        (segment.status === 'completed' || segment.status === 'saved') && segment.itemId
      );
    }

    // Compare current segments with last saved state
    return currentSegments.some((current, index) => {
      const saved = lastSavedState[index];
      if (!saved) {
        // New segment with recognition result
        return current.itemId;
      }

      // Check if key fields have changed
      return current.itemId !== saved.itemId ||
             current.itemName !== saved.itemName ||
             current.isManuallySelected !== saved.isManuallySelected;
    });
  }, [lastSavedState]);

  // Update hasUnsavedChanges whenever timelineSegments change
  useEffect(() => {
    const hasChanges = detectUnsavedChanges(timelineSegments);
    setHasUnsavedChanges(hasChanges);
  }, [timelineSegments, detectUnsavedChanges]);

  const updateSegmentResult = useCallback((segmentIndex: number, itemType: number, confidence: number, itemId?: string, itemName?: string) => {
    setTimelineSegments(prev =>
      prev.map((segment, index) =>
        index === segmentIndex ? {
          ...segment,
          status: 'completed' as const,
          productType: itemType,
          confidence,
          itemId,
          itemName,
          // Store only the actual recognition result
          allRecognitionResults: itemId ? [{
            itemId,
            itemName: itemName || '',
            confidence
          }] : []
        } : segment
      )
    );
  }, []);

  // Handle manual selection of product
  const handleManualSelect = useCallback((segmentIndex: number, selectedItem: { itemId: string; itemName: string; confidence: number }) => {
    setTimelineSegments(prev =>
      prev.map((segment, index) =>
        index === segmentIndex ? {
          ...segment,
          // Store original recognition result
          originalItemId: segment.itemId,
          originalItemName: segment.itemName,
          originalConfidence: segment.confidence,
          // Update with manually selected item
          itemId: selectedItem.itemId,
          itemName: selectedItem.itemName,
          confidence: selectedItem.confidence,
          isManuallySelected: true
        } : segment
      )
    );
  }, []);



  const startHeartbeat = () => {
    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000); // Send ping every 30 seconds
  };

  const stopHeartbeat = () => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  };

  // Initialize timeline segments when video record changes
  useEffect(() => {
    if (videoRecord?.duration) {
      console.log('Initializing timeline for video:', videoRecord.id, 'duration:', videoRecord.duration);
      const segmentDurationMs = 10 * 1000;
      const totalSegs = Math.ceil(videoRecord.duration / segmentDurationMs);

      const segments: TimelineSegment[] = [];
      for (let i = 0; i < totalSegs; i++) {
        segments.push({
          startTime: i * segmentDurationMs,
          endTime: Math.min((i + 1) * segmentDurationMs, videoRecord.duration),
          status: 'pending'
        });
      }

      console.log('Generated timeline segments:', segments.length);
      setTimelineSegments(segments);
      setTotalSegments(totalSegs);
      setTimelineInitialized(true);
    } else {
      setTimelineInitialized(false);
    }
  }, [videoRecord]);

  // Load historical markings when timeline is initialized and modal is visible
  useEffect(() => {
    if (timelineInitialized && visible && videoRecord?.id) {
      console.log('Timeline initialized and modal visible, loading historical markings...');
      loadHistoricalMarkings();
    }
  }, [timelineInitialized, visible, videoRecord?.id]);

  // Generate HLS URL when video record changes
  useEffect(() => {
    if (videoRecord?.id) {
      // Generate HLS URL based on video ID
      const hlsUrl = `/api/fastclip/video/hls/${videoRecord.id}/playlist.m3u8`;
      setHlsVideoUrl(hlsUrl);
    } else {
      setHlsVideoUrl('');
    }
  }, [videoRecord?.id]);

  useEffect(() => {
    if (visible) {
      connectWebSocket();
      // Historical markings will be loaded in the videoRecord effect above
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      stopHeartbeat();
    };
  }, [visible, connectWebSocket]);

  // Video player event handlers
  const handleVideoPlayerReady = (player: any) => {
    console.log('Video player ready:', player);
    setVideoPlayer(player);
  };

  const handleVideoTimeUpdate = (currentTime: number) => {
    setCurrentVideoTime(currentTime);
  };





  const handleStartProcessing = async () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      message.error('WebSocket连接未建立，请稍后重试');
      return;
    }

    if (!videoRecord?.id) {
      message.error('视频信息不完整');
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);
    setCurrentSegment(0);

    // Reset all segments to pending
    setTimelineSegments(prev =>
      prev.map(segment => ({ ...segment, status: 'pending' as const }))
    );

    try {
      // Generate task ID
      const taskId = `task-${Date.now()}-${videoRecord.id}`;
      setCurrentTaskId(taskId);

      // Call Java backend API to start video grounding
      const result = await request('/api/fastclip/item/startVideoGrounding', {
        method: 'POST',
        data: {
          videoId: videoRecord.id,
          videoPath: videoRecord.path,
          taskId: taskId,
          enableRealTimeUpdates: true,
          confidenceThreshold: 0.5
        },
      });

      // console.log('Video grounding result:', result);
      if (result.resultCode === 0) {
        console.log('Video grounding started with task ID:', result.result);
        // WebSocket will handle the real-time updates
      } else {
        throw new Error(result.resultDesc || 'Failed to start video grounding');
      }
    } catch (error) {
      console.error('Error starting video grounding:', error);
      message.error(`启动识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setIsProcessing(false);
    }
  };

  const handleStopProcessing = () => {
    setIsProcessing(false);
    setProcessingProgress(0);
    setCurrentSegment(0);
    // Reset segments to pending
    setTimelineSegments(prev =>
      prev.map(segment => ({ ...segment, status: 'pending' as const }))
    );
    message.info('已停止识别');
  };

  // Save product markings
  const handleSaveMarkings = async () => {
    if (!videoRecord?.id) {
      message.error('视频信息不完整，无法保存');
      return;
    }

    if (!videoRecord?.sellerId) {
      message.error('达人信息不完整，无法保存');
      return;
    }

    // Filter segments that have valid item recognition results (including saved segments that may have been modified)
    const validMarkings = timelineSegments.filter(segment =>
      (segment.status === 'completed' || segment.status === 'saved') && segment.itemId
    );

    if (validMarkings.length === 0) {
      message.warning('没有可保存的识别结果，请先完成商品识别');
      return;
    }

    // Double check: only proceed if there are actually unsaved changes
    if (!hasUnsavedChanges) {
      message.info('当前没有未保存的修改');
      return;
    }

    setIsSaving(true);
    const loadingMessage = message.loading('正在保存识别结果...', 0);

    try {
      // Validate and prepare markings data
      const markings = validMarkings.map(segment => {
        const itemId = parseInt(segment.itemId!);
        if (isNaN(itemId)) {
          throw new Error(`无效的商品ID: ${segment.itemId}`);
        }

        if (segment.startTime >= segment.endTime) {
          throw new Error(`无效的时间范围: ${segment.startTime} - ${segment.endTime}`);
        }

        return {
          itemId: itemId,
          startTs: segment.startTime,
          endTs: segment.endTime,
          duration: segment.endTime - segment.startTime
        };
      });

      console.log('Saving markings:', { videoId: videoRecord.id, sellerId: videoRecord.sellerId, markings });

      const response = await request('/api/fastclip/video/saveProductMarkings', {
        method: 'POST',
        data: {
          videoId: videoRecord.id,
          sellerId: videoRecord.sellerId,
          markings: markings
        },
        timeout: 30000, // 30 second timeout
      });

      loadingMessage();

      if (response.resultCode === 0) {
        const result = response.result;
        message.success(`保存成功！新增 ${result.savedCount} 条，更新 ${result.updatedCount} 条，共处理 ${result.totalCount} 条记录`);

        // Update segments status to saved and save current state
        setTimelineSegments(prev => {
          const updatedSegments = prev.map(segment => {
            if ((segment.status === 'completed' || segment.status === 'saved') && segment.itemId) {
              return { ...segment, status: 'saved' as const, isSaved: true };
            }
            return segment;
          });

          // Save the current state as the last saved state
          setLastSavedState([...updatedSegments]);
          setHasUnsavedChanges(false);

          return updatedSegments;
        });
      } else {
        throw new Error(response.resultDesc || '保存失败，请重试');
      }
    } catch (error) {
      loadingMessage();
      console.error('Error saving markings:', error);

      let errorMessage = '保存失败';
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = '保存超时，请检查网络连接后重试';
        } else if (error.message.includes('Network Error')) {
          errorMessage = '网络错误，请检查网络连接';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else {
          errorMessage = `保存失败: ${error.message}`;
        }
      }

      message.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // Load historical markings
  const loadHistoricalMarkings = async () => {
    if (!videoRecord?.id) {
      console.warn('No video ID available for loading historical markings');
      return;
    }

    try {
      console.log('Loading historical markings for videoId:', videoRecord.id);

      const response = await request('/api/fastclip/video/getProductMarkings', {
        method: 'POST',
        data: {
          videoId: videoRecord.id
        },
        timeout: 15000, // 15 second timeout
      });

      if (response.resultCode === 0) {
        if (response.result?.length > 0) {
          const historicalMarkings = response.result;
          console.log('Found historical markings:', historicalMarkings);
          console.log('Current timeline segments before processing:', timelineSegments.map(s => ({
            startTime: s.startTime,
            endTime: s.endTime,
            status: s.status
          })));

          // Update timeline segments with historical data
          setTimelineSegments(prev => {
            console.log('Current timeline segments before merge:', prev.length);
            console.log('Historical markings to merge:', historicalMarkings.length);

            const updatedSegments = [...prev];
            let addedCount = 0;
            let updatedCount = 0;

            historicalMarkings.forEach((marking: any, index: number) => {
              // Validate marking data - be careful with 0 values!
              if (typeof marking.startTs !== 'number' ||
                  typeof marking.endTs !== 'number' ||
                  !marking.itemId) {
                console.warn(`Invalid historical marking data at index ${index}:`, marking);
                console.warn(`  startTs: ${marking.startTs} (${typeof marking.startTs})`);
                console.warn(`  endTs: ${marking.endTs} (${typeof marking.endTs})`);
                console.warn(`  itemId: ${marking.itemId} (${typeof marking.itemId})`);
                return;
              }

              console.log(`Processing marking ${index + 1}/${historicalMarkings.length}:`, {
                startTs: marking.startTs,
                endTs: marking.endTs,
                itemId: marking.itemId,
                itemName: marking.itemName
              });

              // Find matching segment by time range
              const segmentIndex = updatedSegments.findIndex(segment =>
                segment.startTime === marking.startTs && segment.endTime === marking.endTs
              );

              console.log(`Looking for segment with startTime=${marking.startTs}, endTime=${marking.endTs}, found at index: ${segmentIndex}`);

              if (segmentIndex >= 0) {
                // Update existing segment
                console.log(`Updating existing segment at index ${segmentIndex}`);
                updatedSegments[segmentIndex] = {
                  ...updatedSegments[segmentIndex],
                  status: 'saved' as const,
                  itemId: marking.itemId?.toString(),
                  itemName: marking.itemName,
                  isSaved: true,
                  savedId: marking.id
                };
                updatedCount++;
              } else {
                // Add new segment for historical marking
                console.log(`Adding new segment for marking: startTime=${marking.startTs}, endTime=${marking.endTs}`);
                updatedSegments.push({
                  startTime: marking.startTs,
                  endTime: marking.endTs,
                  status: 'saved' as const,
                  itemId: marking.itemId?.toString(),
                  itemName: marking.itemName,
                  isSaved: true,
                  savedId: marking.id
                });
                addedCount++;
              }
            });

            console.log(`Historical markings processed: ${updatedCount} updated, ${addedCount} added`);
            console.log('Updated segments before sorting:', updatedSegments.map(s => ({
              startTime: s.startTime,
              endTime: s.endTime,
              status: s.status,
              itemId: s.itemId
            })));

            // Sort segments by start time
            const sortedSegments = updatedSegments.sort((a, b) => a.startTime - b.startTime);
            console.log('Final sorted segments:', sortedSegments.map(s => ({
              startTime: s.startTime,
              endTime: s.endTime,
              status: s.status,
              itemId: s.itemId
            })));

            // Initialize state management after loading historical data
            setLastSavedState([...sortedSegments]);
            setHasUnsavedChanges(false);

            return sortedSegments;
          });

          message.success(`成功加载 ${historicalMarkings.length} 条历史标记`);
        } else {
          console.log('No historical markings found for videoId:', videoRecord.id);
          // Initialize state management for empty historical data
          setLastSavedState([...timelineSegments]);
          setHasUnsavedChanges(false);
          // Don't show message for empty results as it's normal
        }
      } else {
        throw new Error(response.resultDesc || '查询历史标记失败');
      }
    } catch (error) {
      console.error('Error loading historical markings:', error);

      let errorMessage = '加载历史标记失败';
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = '加载超时，请检查网络连接';
        } else if (error.message.includes('Network Error')) {
          errorMessage = '网络错误，无法加载历史标记';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器错误，无法加载历史标记';
        } else {
          errorMessage = `加载历史标记失败: ${error.message}`;
        }
      }

      // Only show error message if it's not a network connectivity issue during initial load
      message.warning(errorMessage);
    }
  };



  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED: return '已连接';
      case ConnectionStatus.CONNECTING: return '连接中...';
      case ConnectionStatus.ERROR: return '连接错误';
      default: return '未连接';
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED: return 'success';
      case ConnectionStatus.CONNECTING: return 'processing';
      case ConnectionStatus.ERROR: return 'error';
      default: return 'default';
    }
  };

  return (
    <Modal
      title="商品标记"
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      className={styles.productMarkingModal}
      style={{ top: 20 }}
      styles={{ body: { maxHeight: '85vh', overflowY: 'auto' } }}
    >
      <div className={styles.modalContent}>

        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <PlayCircleOutlined />
              视频信息
            </div>
          }
          size="small"
          style={{ marginBottom: 16 }}
          extra={
            <Tag color={getConnectionStatusColor()}>
              {getConnectionStatusText()}
            </Tag>
          }
        >
          <Row gutter={16}>
            <Col span={8}>
              <div><strong>视频ID:</strong> {videoRecord?.id}</div>
            </Col>
            <Col span={8}>
              <div><strong>达人:</strong> {videoRecord?.sellerName}</div>
            </Col>
            <Col span={8}>
              <div><strong>时长:</strong> {formatTime(videoRecord?.duration || 0)}</div>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 8 }}>
            <Col span={12}>
              <div><strong>素材路径:</strong> {videoRecord?.path}</div>
            </Col>
            <Col span={12}>
              <div><strong>素材日期:</strong> {videoRecord?.startDate}</div>
            </Col>
          </Row>
        </Card>

        {isProcessing && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <div style={{ textAlign: 'center' }}>
              <Spin size="small" style={{ marginRight: 8 }} />
              正在识别视频商品...
            </div>
            <Progress
              percent={Math.round(processingProgress)}
              status="active"
              format={(percent) => `${currentSegment}/${totalSegments} (${percent}%)`}
              style={{ marginTop: 12 }}
            />
            <div style={{ marginTop: 8, textAlign: 'center', color: '#666', fontSize: '12px' }}>
              正在处理第 {currentSegment} 个片段，共 {totalSegments} 个片段
            </div>
          </Card>
        )}

        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <ClockCircleOutlined />
              视频预览与标注
            </div>
          }
          size="small"
          extra={
            timelineSegments.length > 0 && (
              <div style={{ display: 'flex', gap: 8 }}>
                {!isProcessing ? (
                  <>
                    <Button
                      type="primary"
                      onClick={handleStartProcessing}
                      disabled={connectionStatus !== ConnectionStatus.CONNECTED}
                      icon={<PlayCircleOutlined />}
                    >
                      开始识别
                    </Button>
                    <Button
                      onClick={handleSaveMarkings}
                      disabled={isSaving || !hasUnsavedChanges}
                      loading={isSaving}
                      icon={<SaveOutlined />}
                      type={hasUnsavedChanges ? "primary" : "default"}
                    >
                      {hasUnsavedChanges ? "保存修改" : "已保存"}
                    </Button>
                  </>
                ) : (
                  <Button
                    danger
                    onClick={handleStopProcessing}
                    icon={<StopOutlined />}
                  >
                    停止识别
                  </Button>
                )}
              </div>
            )
          }
        >
          {/* Video Player Section */}
          {hlsVideoUrl && (
            <div style={{ marginBottom: 16 }}>
              <VideoPlayer
                src={hlsVideoUrl}
                onReady={handleVideoPlayerReady}
                onTimeUpdate={handleVideoTimeUpdate}
              />
            </div>
          )}

          {/* Timeline Section */}
          <VideoTimeline
            segments={timelineSegments}
            isProcessing={isProcessing}
            onSegmentClick={(segment) => {
              console.log('Clicked segment:', segment);
              // Jump video player to segment start time
              if (videoPlayer && segment.startTime !== undefined) {
                const timeInSeconds = segment.startTime / 1000; // Convert milliseconds to seconds
                try {
                  videoPlayer.currentTime(timeInSeconds);
                  if (videoPlayer.paused()) {
                    videoPlayer.play();
                  }
                  console.log(`Video jumped to time: ${timeInSeconds}s`);
                } catch (error) {
                  console.error('Failed to jump video to time:', error);
                }
              }
            }}
            onManualSelect={handleManualSelect}
          />
        </Card>
      </div>
    </Modal>
  );
};

export default ProductMarkingModal;
